# 标签打印机功能优化说明

## 优化内容

### 1. 强制打印逻辑优化

#### 问题描述
原来的逻辑是：当耗材不匹配且 `forcePrint` 为 false 时才弹出确认对话框，这与需求不符。

#### 优化后的逻辑
- **当 `forcePrint` 为 true 时**：如果耗材不匹配，会弹出确认对话框让用户选择是否强制打印
- **当 `forcePrint` 为 false 时**：如果耗材不匹配，直接阻止打印并提示用户

#### 代码变更
```javascript
// 检查耗材规格兼容性
if (this.data.materialMismatch) {
  // 如果设置了强制打印，弹出确认对话框
  if (this.data.forcePrint) {
    wx.showModal({
      title: '耗材规格不匹配',
      content: '当前耗材规格与模版不一致，确定要强制打印吗？',
      confirmText: '确定打印',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.executePrint()
        }
      }
    })
    return
  } else {
    // 如果没有设置强制打印，直接阻止打印
    wx.showToast({
      title: '耗材规格不匹配，无法打印',
      icon: 'none',
      duration: 2000
    })
    return
  }
}
```

### 2. 模板切换预览优化

#### 问题描述
快速切换模板时，由于预览图生成有延时，可能出现选择A模板但显示B模板内容的问题。

#### 优化方案
1. **添加预览生成状态控制**：防止重复请求
2. **使用预览ID机制**：确保只有最新的预览请求结果被应用
3. **忽略过期请求**：旧的预览请求结果会被自动忽略

#### 代码变更
```javascript
// 数据结构增加
data: {
  previewGenerating: false, // 是否正在生成预览图
  currentPreviewId: 0, // 当前预览图生成ID，用于防止竞态条件
}

// 预览生成逻辑优化
generatePreview() {
  // 如果正在生成预览图，跳过本次请求
  if (this.data.previewGenerating) {
    console.log('预览图正在生成中，跳过本次请求')
    return
  }

  // 生成唯一的预览ID，用于防止竞态条件
  const previewId = ++this.data.currentPreviewId
  
  // 设置生成状态
  this.setData({
    previewGenerating: true
  })

  // 在回调中检查预览ID
  bleToothManage.doDrawPreview(/* ... */, res => {
    // 检查是否为最新的预览请求
    if (previewId !== that.data.currentPreviewId) {
      console.log(`预览图请求已过期，忽略结果`)
      return
    }
    // 处理预览结果...
  })
}
```

## 新增功能

### 强制打印编程接口

#### 设置强制打印标志
```javascript
/**
 * 设置强制打印标志（编程接口）
 * @param {boolean} force - 是否允许强制打印
 */
setForcePrint(force)
```

#### 获取强制打印状态
```javascript
/**
 * 获取当前强制打印标志状态
 * @returns {boolean} 当前强制打印标志状态
 */
getForcePrint()
```

### 使用示例

#### 1. 根据用户权限设置
```javascript
// 管理员可以强制打印
if (userRole === 'admin') {
  this.setForcePrint(true)
} else {
  this.setForcePrint(false)
}
```

#### 2. 临时启用强制打印
```javascript
// 紧急情况下临时启用5分钟
this.setForcePrint(true)
setTimeout(() => {
  this.setForcePrint(false)
}, 300000)
```

#### 3. 根据产品类型设置
```javascript
// 特殊产品允许强制打印
const specialProducts = ['urgent', 'sample', 'test']
if (specialProducts.includes(productType)) {
  this.setForcePrint(true)
}
```

## 技术细节

### 竞态条件防护
- 使用递增的预览ID来标识每次预览请求
- 只有最新的预览请求结果会被应用到界面
- 过期的预览请求结果会被自动忽略

### 状态管理
- `previewGenerating`：防止重复的预览生成请求
- `currentPreviewId`：用于识别最新的预览请求
- `forcePrint`：控制强制打印行为

### 持久化存储
- 强制打印设置可选择性地保存到本地存储
- 下次启动时会自动恢复设置

## 注意事项

1. **强制打印控制**：`forcePrint` 标志只能通过代码控制，不提供用户界面
2. **预览性能**：优化后的预览机制可以避免不必要的重复请求
3. **用户体验**：耗材不匹配时的提示更加清晰明确
4. **安全性**：强制打印功能需要通过代码权限控制，避免误用

## 兼容性

- 所有现有功能保持不变
- 新增的接口不影响原有逻辑
- 可以渐进式地启用新功能
