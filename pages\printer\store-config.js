/**
 * 在线商城配置文件
 * 
 * 此文件用于配置在线商城的跳转参数和相关设置
 * 可以根据实际需求修改配置项
 */

// 商城配置
const storeConfig = {
  // 跳转方式：'miniprogram' | 'webview' | 'page' | 'options'
  type: 'options', // 默认显示选项，待配置完成后修改

  // 小程序跳转配置
  miniprogram: {
    appId: '', // 目标小程序AppId，例如：'wx1234567890abcdef'
    path: '', // 目标页面路径，例如：'pages/index/index'
    extraData: {
      // 传递给目标小程序的数据
      source: 'printer_app',
      // 可以添加更多自定义数据
    },
    envVersion: 'release' // 版本：develop(开发版), trial(体验版), release(正式版)
  },

  // 网页跳转配置
  webview: {
    url: '', // 商城网址，例如：'https://store.example.com'
    // 是否需要创建webview页面来承载
    needWebviewPage: true
  },

  // 内部页面跳转配置
  page: {
    url: '/pages/store/store' // 内部商城页面路径
  },

  // 备选方案配置
  fallback: {
    showContact: true, // 是否显示联系客服选项
    showProductInfo: true, // 是否显示产品信息选项
    customOptions: [
      // 可以添加自定义选项
      // {
      //   name: '官方网站',
      //   action: 'website',
      //   url: 'https://www.example.com'
      // }
    ]
  }
}

// 产品信息配置
const productConfig = {
  // 推荐产品列表
  recommendedProducts: [
    {
      name: '标签纸 40×30mm',
      specs: {
        width: 40,
        height: 30,
        gap: 8
      },
      description: '适用于小型标签打印',
      price: '¥XX.XX' // 价格信息，待补充
    },
    {
      name: '标签纸 50×28mm',
      specs: {
        width: 50,
        height: 28,
        gap: 3
      },
      description: '适用于中型标签打印',
      price: '¥XX.XX' // 价格信息，待补充
    }
    // 可以添加更多产品
  ],

  // 耗材兼容性提示
  compatibilityTips: {
    match: '✅ 当前耗材与模板完全匹配',
    mismatch: '⚠️ 当前耗材与模板不匹配，建议购买匹配规格的耗材',
    unknown: 'ℹ️ 请连接打印机获取耗材信息'
  }
}

// 联系方式配置
const contactConfig = {
  serviceHotline: '400-622-9388',
  serviceFax: '020-89577250',
  serviceEmail: '<EMAIL>',
  wechatPublic: '惠而信',
  address: '广州市海珠区泉塘路 2 号之三（浩诚商务中心）605 惠而信'
}

// 导出配置
module.exports = {
  storeConfig,
  productConfig,
  contactConfig
}

/**
 * 配置说明：
 * 
 * 1. 小程序跳转配置：
 *    - 需要在微信公众平台配置业务域名
 *    - 需要目标小程序的AppId和页面路径
 *    - extraData可以传递当前设备信息
 * 
 * 2. 网页跳转配置：
 *    - 需要配置业务域名白名单
 *    - 可能需要创建webview页面来承载网页
 *    - URL参数会自动添加设备信息
 * 
 * 3. 内部页面跳转：
 *    - 需要创建对应的页面文件
 *    - 可以完全自定义商城界面和功能
 * 
 * 4. 使用方法：
 *    在 printer.js 中引入配置：
 *    const { storeConfig } = require('./store-config.js')
 *    然后使用 this.handleStoreNavigation(storeConfig)
 */
