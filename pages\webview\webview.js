// pages/webview/webview.js
Page({
  data: {
    url: '',
    title: '在线商城'
  },

  onLoad(options) {
    // 获取传入的URL参数
    if (options.url) {
      const decodedUrl = decodeURIComponent(options.url)
      this.setData({
        url: decodedUrl
      })
      
      // 设置页面标题
      if (options.title) {
        const title = decodeURIComponent(options.title)
        this.setData({ title })
        wx.setNavigationBarTitle({ title })
      }
    } else {
      wx.showToast({
        title: '页面地址错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 页面显示时的处理
  },

  onHide() {
    // 页面隐藏时的处理
  },

  onUnload() {
    // 页面卸载时的处理
  },

  /**
   * web-view网页加载成功
   */
  onWebviewLoad(e) {
    console.log('webview加载成功:', e)
  },

  /**
   * web-view网页加载失败
   */
  onWebviewError(e) {
    console.error('webview加载失败:', e)
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    })
  },

  /**
   * web-view网页向小程序发送消息
   */
  onWebviewMessage(e) {
    console.log('收到webview消息:', e)
    const data = e.detail.data
    
    // 处理网页发送的消息
    if (data && data.length > 0) {
      const message = data[data.length - 1] // 获取最新消息
      this.handleWebviewMessage(message)
    }
  },

  /**
   * 处理webview消息
   */
  handleWebviewMessage(message) {
    switch (message.type) {
      case 'close':
        // 关闭页面
        wx.navigateBack()
        break
      case 'share':
        // 分享功能
        this.triggerShare(message.data)
        break
      case 'contact':
        // 联系客服
        this.contactService()
        break
      default:
        console.log('未知消息类型:', message)
        break
    }
  },

  /**
   * 触发分享
   */
  triggerShare(shareData) {
    // 这里可以处理分享逻辑
    console.log('触发分享:', shareData)
  },

  /**
   * 联系客服
   */
  contactService() {
    // 返回到打印机页面并显示联系我们
    wx.navigateBack({
      success: () => {
        // 通过事件通知打印机页面显示联系我们
        const pages = getCurrentPages()
        const printerPage = pages[pages.length - 1]
        if (printerPage && printerPage.showContact) {
          printerPage.showContact()
        }
      }
    })
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    return {
      title: this.data.title,
      path: `/pages/webview/webview?url=${encodeURIComponent(this.data.url)}&title=${encodeURIComponent(this.data.title)}`
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: this.data.title
    }
  }
})
