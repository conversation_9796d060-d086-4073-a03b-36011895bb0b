/**
 * 强制打印功能使用示例
 * 
 * 此文件展示了如何在代码中控制强制打印功能
 * 注意：这只是示例代码，实际使用时请根据具体需求调整
 */

// 示例1：在页面加载时根据用户权限设置强制打印
function initForcePrintByUserRole() {
  // 假设从用户信息中获取权限
  const userInfo = wx.getStorageSync('userInfo') || {}
  const userRole = userInfo.role || 'normal'
  
  // 获取页面实例
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  
  if (userRole === 'admin' || userRole === 'supervisor') {
    // 管理员或主管可以强制打印
    currentPage.setForcePrint(true)
    console.log('管理员权限：已启用强制打印功能')
  } else {
    // 普通用户不允许强制打印
    currentPage.setForcePrint(false)
    console.log('普通用户：已禁用强制打印功能')
  }
}

// 示例2：在特定时间段启用强制打印
function enableForcePrintInSpecialTime() {
  const currentHour = new Date().getHours()
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  
  // 在夜班时间（22:00-06:00）允许强制打印
  if (currentHour >= 22 || currentHour < 6) {
    currentPage.setForcePrint(true)
    console.log('夜班时间：已启用强制打印功能')
  } else {
    currentPage.setForcePrint(false)
    console.log('正常时间：已禁用强制打印功能')
  }
}

// 示例3：根据产品类型设置强制打印
function setForcePrintByProductType(productType) {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  
  // 特殊产品类型允许强制打印
  const specialProducts = ['urgent', 'sample', 'test']
  
  if (specialProducts.includes(productType)) {
    currentPage.setForcePrint(true)
    console.log(`产品类型 ${productType}：已启用强制打印功能`)
  } else {
    currentPage.setForcePrint(false)
    console.log(`产品类型 ${productType}：已禁用强制打印功能`)
  }
}

// 示例4：临时启用强制打印（用于紧急情况）
function temporaryEnableForcePrint(duration = 300000) { // 默认5分钟
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  
  // 启用强制打印
  currentPage.setForcePrint(true)
  console.log('紧急模式：已临时启用强制打印功能')
  
  // 设置定时器，指定时间后自动禁用
  setTimeout(() => {
    currentPage.setForcePrint(false)
    console.log('紧急模式结束：已自动禁用强制打印功能')
    
    wx.showToast({
      title: '紧急模式已结束',
      icon: 'none'
    })
  }, duration)
  
  wx.showToast({
    title: `紧急模式已启用（${duration/1000}秒）`,
    icon: 'none'
  })
}

// 示例5：检查当前强制打印状态
function checkForcePrintStatus() {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  
  const isForceEnabled = currentPage.getForcePrint()
  console.log('当前强制打印状态:', isForceEnabled ? '启用' : '禁用')
  
  return isForceEnabled
}

// 导出示例函数（如果需要在其他地方使用）
module.exports = {
  initForcePrintByUserRole,
  enableForcePrintInSpecialTime,
  setForcePrintByProductType,
  temporaryEnableForcePrint,
  checkForcePrintStatus
}
