# 在线商城小程序跳转配置说明

## 配置步骤

### 1. 获取目标小程序信息

您需要准备以下信息：
- **目标小程序的AppId**：例如 `wx1234567890abcdef`
- **目标页面路径**：例如 `pages/store/index` 或 `pages/index/index`
- **环境版本**：`develop`(开发版) | `trial`(体验版) | `release`(正式版)

### 2. 修改配置文件

在 `pages/printer/store-config.js` 文件中修改以下配置：

```javascript
const storeConfig = {
  type: 'miniprogram',
  miniprogram: {
    appId: 'wx1234567890abcdef', // 替换为实际的AppId
    path: 'pages/store/index',    // 替换为实际的页面路径
    extraData: {
      source: 'printer_app',
      // 可以添加其他自定义数据
    },
    envVersion: 'release'         // 正式版
  }
}
```

### 3. 微信公众平台配置

在微信公众平台需要进行以下配置：

1. **关联小程序**：
   - 登录微信公众平台
   - 进入"设置" → "第三方设置" → "授权管理"
   - 添加目标商城小程序的关联

2. **配置跳转权限**：
   - 确保当前小程序有跳转到目标小程序的权限
   - 在"开发" → "开发设置" → "服务器域名"中配置相关域名

## 传递的数据结构

跳转时会自动传递以下数据给目标小程序：

```javascript
{
  // 基础信息
  source: 'printer_app',
  timestamp: 1640995200000,
  
  // 设备信息
  deviceInfo: {
    printerSn: 'T0145B',           // 打印机序列号
    printerStatus: 'connected',    // 打印机状态
    isConnected: true              // 是否已连接
  },
  
  // 耗材信息
  materialInfo: {
    printHeadDirectionSize: 50,    // 宽度
    paperDirectionSize: 30,        // 高度
    gap: 8                         // 间隙
  },
  
  // 模板信息
  templateInfo: {
    name: '50 * 28 mm',           // 模板名称
    width: 50,                     // 宽度
    height: 28,                    // 高度
    gap: 3,                        // 间隙
    index: 1                       // 模板索引
  },
  
  // 兼容性信息
  compatibility: {
    materialMismatch: true,        // 是否不匹配
    forcePrint: false              // 是否允许强制打印
  },
  
  // 标签内容
  labelContent: {
    productName: '品名',          // 产品名称
    operator: '操作人',           // 操作员
    date: '2024-01-01',           // 日期
    copies: 1                      // 打印份数
  }
}
```

## 目标小程序接收数据

在目标商城小程序中，可以通过以下方式接收数据：

```javascript
// 在目标小程序的页面中
Page({
  onLoad(options) {
    // 获取传递的数据
    const extraData = options.extraData || {}
    console.log('接收到的数据:', extraData)
    
    // 处理设备信息
    if (extraData.deviceInfo) {
      this.handleDeviceInfo(extraData.deviceInfo)
    }
    
    // 处理耗材信息
    if (extraData.materialInfo) {
      this.handleMaterialInfo(extraData.materialInfo)
    }
  }
})
```

## 错误处理

系统会自动处理以下错误情况：

1. **AppId未配置**：显示配置提示，引导联系客服
2. **目标小程序不存在**：显示"目标小程序不存在或未发布"
3. **用户取消跳转**：显示"用户取消跳转"
4. **其他错误**：显示"跳转失败，请稍后重试"

## 测试建议

1. **开发阶段**：
   - 设置 `envVersion: 'develop'`
   - 使用开发版小程序进行测试

2. **体验阶段**：
   - 设置 `envVersion: 'trial'`
   - 邀请用户体验测试

3. **正式发布**：
   - 设置 `envVersion: 'release'`
   - 确保目标小程序已正式发布

## 注意事项

1. **权限配置**：确保在微信公众平台正确配置了小程序关联
2. **数据安全**：传递的数据会经过微信平台，注意不要传递敏感信息
3. **版本兼容**：确保目标小程序能正确处理传递的数据格式
4. **用户体验**：跳转失败时提供友好的错误提示和备选方案

## 快速配置示例

如果您的商城小程序AppId是 `wxabcd1234efgh5678`，首页路径是 `pages/index/index`，则配置如下：

```javascript
const storeConfig = {
  type: 'miniprogram',
  miniprogram: {
    appId: 'wxabcd1234efgh5678',
    path: 'pages/index/index',
    extraData: {
      source: 'printer_app'
    },
    envVersion: 'release'
  }
}
```

配置完成后，用户点击"在线商城"按钮就会跳转到您的商城小程序！
